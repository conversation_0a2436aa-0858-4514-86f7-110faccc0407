D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\appsettings.Development.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\appsettings.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\last_received_data.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\last_received_data_046c207e_20250520_174310.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\last_received_data_d6bcd2bb_20250520_174223.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\libman.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\XECOM Client-CloudServer.staticwebassets.endpoints.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\XECOM Client-CloudServer.exe
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\XECOM Client-CloudServer.deps.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\XECOM Client-CloudServer.runtimeconfig.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\XECOM Client-CloudServer.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\XECOM Client-CloudServer.pdb
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Azure.Core.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Azure.Identity.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Humanizer.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.AspNetCore.Connections.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.AspNetCore.Http.Connections.Client.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.AspNetCore.Http.Connections.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.AspNetCore.SignalR.Client.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.AspNetCore.SignalR.Client.Core.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.AspNetCore.SignalR.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.AspNetCore.SignalR.Protocols.Json.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Bcl.TimeProvider.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Build.Locator.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.CodeAnalysis.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.CodeAnalysis.CSharp.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.CodeAnalysis.Workspaces.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.CodeAnalysis.Workspaces.MSBuild.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Data.SqlClient.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.EntityFrameworkCore.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.EntityFrameworkCore.Design.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.EntityFrameworkCore.SqlServer.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.Caching.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.Caching.Memory.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.DependencyInjection.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.DependencyModel.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.Features.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.Logging.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.Options.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Extensions.Primitives.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Identity.Client.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.IdentityModel.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.IdentityModel.Logging.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.IdentityModel.Protocols.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.IdentityModel.Tokens.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.OpenApi.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.SqlServer.Server.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Microsoft.Win32.SystemEvents.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Mono.TextTemplating.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Newtonsoft.Json.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Serilog.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Serilog.Enrichers.Process.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Serilog.Enrichers.Thread.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Serilog.Extensions.Hosting.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Serilog.Extensions.Logging.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Serilog.Settings.Configuration.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Serilog.Sinks.Console.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Serilog.Sinks.File.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Swashbuckle.AspNetCore.Swagger.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.ClientModel.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.CodeDom.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Composition.AttributedModel.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Composition.Convention.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Composition.Hosting.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Composition.Runtime.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Composition.TypedParts.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Configuration.ConfigurationManager.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Diagnostics.DiagnosticSource.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Drawing.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Formats.Asn1.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.IdentityModel.Tokens.Jwt.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.IO.Pipelines.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Memory.Data.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Net.ServerSentEvents.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Runtime.Caching.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Security.Cryptography.ProtectedData.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Security.Permissions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Text.Encodings.Web.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Text.Json.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Threading.Channels.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\System.Windows.Extensions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\de\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\es\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\it\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\de\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\es\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\it\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\de\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\es\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\it\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\unix\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\win\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\win\lib\net6.0\System.Drawing.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\win\lib\net6.0\System.Runtime.Caching.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\bin\Release\net8.0\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\XECOM Client-CloudServer.csproj.AssemblyReference.cache
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\XECOM Client-CloudServer.GeneratedMSBuildEditorConfig.editorconfig
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\XECOM Client-CloudServer.AssemblyInfoInputs.cache
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\XECOM Client-CloudServer.AssemblyInfo.cs
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\XECOM Client-CloudServer.csproj.CoreCompileInputs.cache
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\XECOM Client-CloudServer.MvcApplicationPartsAssemblyInfo.cs
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\XECOM Client-CloudServer.MvcApplicationPartsAssemblyInfo.cache
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\scopedcss\bundle\XECOM Client-CloudServer.styles.css
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\staticwebassets.build.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\staticwebassets.development.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\staticwebassets.build.endpoints.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\staticwebassets\msbuild.XECOM Client-CloudServer.Microsoft.AspNetCore.StaticWebAssets.props
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\staticwebassets\msbuild.XECOM Client-CloudServer.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\staticwebassets\msbuild.build.XECOM Client-CloudServer.props
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\staticwebassets\msbuild.buildMultiTargeting.XECOM Client-CloudServer.props
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\staticwebassets\msbuild.buildTransitive.XECOM Client-CloudServer.props
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\staticwebassets.pack.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\staticwebassets.upToDateCheck.txt
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\XECOM Cl.0C38F5D2.Up2Date
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\XECOM Client-CloudServer.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\refint\XECOM Client-CloudServer.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\XECOM Client-CloudServer.pdb
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\XECOM Client-CloudServer.genruntimeconfig.cache
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\ref\XECOM Client-CloudServer.dll
