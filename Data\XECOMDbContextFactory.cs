﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using System.IO;

namespace XECOM_Client_CloudServer.Data
{
    public class XECOMDbContextFactory : IDesignTimeDbContextFactory<XECOMDbContext>
    {
        public XECOMDbContext CreateDbContext(string[] args)
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json")
                .Build();

            var optionsBuilder = new DbContextOptionsBuilder<XECOMDbContext>();
            optionsBuilder.UseSqlServer(configuration.GetConnectionString("DefaultConnection"));

            return new XECOMDbContext(optionsBuilder.Options);
        }
    }
}
