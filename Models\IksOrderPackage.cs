﻿// Models/Orders/IksOrderPackage.cs
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XECOM_Client_CloudServer.Models
{
    [Table("iks_OrderPackages")]
    public class IksOrderPackage
    {
        [Key]
        public long Id { get; set; }
        public string IkasId { get; set; }

        [ForeignKey(nameof(Order))]
        public long OrderId { get; set; }

        public bool? Deleted { get; set; }
        public string ErrorMessage { get; set; }
        public string Note { get; set; }
        public string OrderPackageFulfillStatus { get; set; }
        public string OrderPackageNumber { get; set; }
        public string StockLocationId { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string TrackingBarcode { get; set; }
        public string TrackingCargoCompany { get; set; }
        public string TrackingLink { get; set; }
        public string TrackingNumber { get; set; }
        public string OrderLineItemIds { get; set; }

        public virtual IksOrder Order { get; set; }
    }
}
