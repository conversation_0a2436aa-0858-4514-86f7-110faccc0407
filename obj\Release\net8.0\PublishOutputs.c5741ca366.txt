D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\XECOM Client-CloudServer.exe
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\appsettings.Development.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\appsettings.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\last_received_data.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\last_received_data_046c207e_20250520_174310.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\last_received_data_d6bcd2bb_20250520_174223.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\libman.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\XECOM Client-CloudServer.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\XECOM Client-CloudServer.runtimeconfig.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\XECOM Client-CloudServer.pdb
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Azure.Core.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Azure.Identity.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.AspNetCore.Connections.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.AspNetCore.Http.Connections.Client.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.AspNetCore.Http.Connections.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.AspNetCore.SignalR.Client.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.AspNetCore.SignalR.Client.Core.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.AspNetCore.SignalR.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.AspNetCore.SignalR.Protocols.Json.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Bcl.AsyncInterfaces.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Bcl.TimeProvider.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Data.SqlClient.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.EntityFrameworkCore.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.EntityFrameworkCore.Relational.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.EntityFrameworkCore.SqlServer.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Caching.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Caching.Memory.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Configuration.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.DependencyInjection.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.DependencyModel.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Diagnostics.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Features.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Hosting.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Logging.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Logging.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Options.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Primitives.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Identity.Client.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Identity.Client.Extensions.Msal.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.IdentityModel.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.IdentityModel.JsonWebTokens.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.IdentityModel.Logging.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.IdentityModel.Protocols.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.IdentityModel.Tokens.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.OpenApi.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.SqlServer.Server.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Microsoft.Win32.SystemEvents.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Newtonsoft.Json.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Serilog.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Serilog.Enrichers.Process.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Serilog.Enrichers.Thread.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Serilog.Extensions.Hosting.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Serilog.Extensions.Logging.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Serilog.Settings.Configuration.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Serilog.Sinks.Console.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Serilog.Sinks.File.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Swashbuckle.AspNetCore.Swagger.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.ClientModel.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Configuration.ConfigurationManager.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Diagnostics.DiagnosticSource.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Drawing.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Formats.Asn1.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.IdentityModel.Tokens.Jwt.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.IO.Pipelines.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Memory.Data.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Net.ServerSentEvents.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Runtime.Caching.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Security.Cryptography.ProtectedData.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Security.Permissions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Text.Encodings.Web.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Text.Json.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Threading.Channels.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\System.Windows.Extensions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\unix\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\win\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\win\lib\net6.0\System.Drawing.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\win\lib\net6.0\System.Runtime.Caching.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Client-CloudServer\XECOM Client-CloudServer\obj\Release\net8.0\PubTmp\Out\XECOM Client-CloudServer.deps.json
