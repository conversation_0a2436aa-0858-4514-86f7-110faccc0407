﻿// JsonStringConverter.cs
// JSON verisini okurken, sayısal veriyi otomatik olarak string olarak dönüştüren özel bir JSON dönüştürücü
using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace XECOM_Client_CloudServer.Services
{
    public class JsonStringConverter : JsonConverter<string>
    {
        // JSON verisini string olarak okur, eğer veri sayısal bir değer içeriyorsa stringe dönüştürerek döndürür
        public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            // Gelen veri sayısal ise string olarak dönüştür ve döndür
            if (reader.TokenType == JsonTokenType.Number)
            {
                return reader.GetDouble().ToString(); // veya reader.GetInt32().ToString() gibi kullanılabilir
            }
            // Gelen veri zaten string ise doğrudan döndür
            if (reader.TokenType == JsonTokenType.String)
            {
                return reader.GetString();
            }
            return null;
        }

        // JSON verisini string olarak yazar
        public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value);
        }
    }
}
