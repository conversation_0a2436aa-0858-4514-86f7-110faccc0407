﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XECOM_Client_CloudServer.Models
{
    public class Product
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long ID { get; set; }

        public string? Category { get; set; }

        [Column("assortment_type")]
        public string? AssortmentType { get; set; }

        [Required]
        public string Name { get; set; }

        public string? Brand { get; set; }

        [Column("Model_ID")]
        public string? ModelID { get; set; }

        [Column("SKU_ID")]
        [Required]
        [StringLength(100)]
        public string SKU_ID { get; set; }

        public string? Target_Age_Groups { get; set; }
        public string? Target_Genders { get; set; }
        public string? Season { get; set; }
        public string? Description { get; set; }

        public string? Material_Upper_Material_Clothing1_Ratio { get; set; }
        public string? Material_Upper_Material_Clothing1 { get; set; }
        public string? Material_Upper_Material_Clothing2_Ratio { get; set; }
        public string? Material_Upper_Material_Clothing2 { get; set; }
        public string? Material_Upper_Material_Clothing3_Ratio { get; set; }
        public string? Material_Upper_Material_Clothing3 { get; set; }
        public string? Material_Upper_Material_Clothing4_Ratio { get; set; }
        public string? Material_Upper_Material_Clothing4 { get; set; }

        public string? Media1 { get; set; }
        public string? Media2 { get; set; }
        public string? Media3 { get; set; }
        public string? Media4 { get; set; }
        public string? Media5 { get; set; }

        public string? Color_Code_Primary { get; set; }
        public string? Supplier_Color { get; set; }
        public string? Size { get; set; }

        [NotMapped]
        public string? Size1 { get; set; }

        [Column("EAN")]
        [Required]
        [StringLength(13)]
        public string EAN { get; set; }

        [Required]
        public string Stock_Quantity { get; set; }

        [Column("regular_price")]
        public string? RegularPrice { get; set; }

        public string? Discount { get; set; }
        public string? Currency { get; set; }
        public string? LanguageCode { get; set; }
        public string? CargoDesi { get; set; }
        public string? DeliveryCosts { get; set; }

        [Required]
        public DateTime UpdateDate { get; set; } = DateTime.UtcNow;

        public DateTime? RecordDate { get; set; } = DateTime.UtcNow;
    }
}