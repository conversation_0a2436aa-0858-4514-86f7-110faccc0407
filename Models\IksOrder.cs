﻿// Models/Orders/IksOrder.cs
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Net;

namespace XECOM_Client_CloudServer.Models
{
    [Table("iks_Orders")]
    public class IksOrder
    {
        [Key]
        public long Id { get; set; }

        public string IkasId { get; set; }
        public string OrderNumber { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string Status { get; set; }
        public string OrderPaymentStatus { get; set; }
        public decimal? TotalPrice { get; set; }
        public decimal? TotalFinalPrice { get; set; }
        public string MerchantId { get; set; }
        public bool? Deleted { get; set; }
        public string CancelReason { get; set; }
        public DateTime? CancelledAt { get; set; }
        public string ClientIp { get; set; }
        public string Host { get; set; }
        public string UserAgent { get; set; }
        public bool? IsGiftPackage { get; set; }
        public string GiftPackageNote { get; set; }
        public string OrderPackageStatus { get; set; }
        public int? OrderSequence { get; set; }
        public DateTime? OrderedAt { get; set; }
        public long? BillingAddressId { get; set; }
        public long? ShippingAddressId { get; set; }
        public DateTime? InsertedAt { get; set; }
        public DateTime? RecordDate { get; set; }
        public DateTime? SyncUpdateAt { get; set; }

        // Navigation (opsiyonel; EF ilişkisiyle kullanmak isterseniz)
        public virtual IksAddress BillingAddress { get; set; }
        public virtual IksAddress ShippingAddress { get; set; }
        public virtual ICollection<IksOrderLineItem> OrderLineItems { get; set; }
        public virtual ICollection<IksOrderPackage> OrderPackages { get; set; }
    }
}
