﻿using Microsoft.Extensions.Configuration;
using Serilog;
using System.Threading.Tasks;
using XECOM_Client_CloudServer.Data;


namespace XECOM_Client_CloudServer.Services
{
    public static class ApplicationService
    {
        /// <summary>
        /// StartApplicationAsync => DbContext oluştur, SignalR bağlantısını kur,
        /// HandleSignalREventsAsync'i çağır.
        /// 
        /// Bu metod "Program.cs" içinde "await ApplicationService.StartApplicationAsync(...)" şeklinde kullanılır.
        /// </summary>
        public static async Task StartApplicationAsync(
        IConfiguration configuration,
        XECOMDbContext dbContext, // Bu parametre artık gerekli değil
        Serilog.ILogger logger)
        {
            logger.Information("[ApplicationService] Starting application logic...");

            // 1) HubConnection => Initialize
            var hubConnection = await SignalRService.InitializeConnectionAsync(configuration, logger);

            // 2) Subscribe to events => "ReceiveData" (artık dbContext parametre olarak gönderilmeyecek)
            await SignalRService.HandleSignalREventsAsync(hubConnection, configuration, logger);

            logger.Information("[ApplicationService] Application logic started. Events subscribed.");
        }
    }
}
