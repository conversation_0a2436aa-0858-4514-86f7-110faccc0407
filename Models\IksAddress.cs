﻿// Models/Orders/IksAddress.cs
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XECOM_Client_CloudServer.Models
{
    [Table("iks_Addresses")]
    public class IksAddress
    {
        [Key]
        public long Id { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string CityName { get; set; }
        public string DistrictName { get; set; }
        public string StateName { get; set; }
        public string CountryName { get; set; }
        public string Phone { get; set; }
        public string PostalCode { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Company { get; set; }
        public string IdentityNumber { get; set; }
        public bool? IsDefault { get; set; }
        public string TaxNumber { get; set; }
        public string TaxOffice { get; set; }
    }
}

