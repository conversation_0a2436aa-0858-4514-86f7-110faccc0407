﻿// Services/DatabaseService.cs
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Serilog;
using System.Threading.Tasks;
using XECOM_Client_CloudServer.Data;

namespace XECOM_Client_CloudServer.Services
{
    public static class DatabaseService
    {
        /// <summary>
        /// Komut satırında "migrate" dendiğinde EF Core migrations çalıştırır.
        /// </summary>
        public static async Task RunMigrationAsync(IConfiguration configuration)
        {
            Log.Information("[DatabaseService] Migrasyon işlemi başlatılıyor...");
            var dbContext = CreateDbContext(configuration);
            await dbContext.Database.MigrateAsync();
            Log.Information("[DatabaseService] Migrasyon işlemi tamamlandı.");
        }

        /// <summary>
        /// Komut satırında "update" dendiğinde veritabanını oluşturur veya günceller.
        /// </summary>
        public static async Task UpdateDatabaseAsync(IConfiguration configuration)
        {
            Log.Information("[DatabaseService] Veritabanı güncelleme işlemi başlatılıyor...");
            var dbContext = CreateDbContext(configuration);
            await dbContext.Database.EnsureCreatedAsync();
            Log.Information("[DatabaseService] Veritabanı güncelleme işlemi tamamlandı.");
        }

        /// <summary>
        /// EF Core DbContext yaratır. ConnectionString "appsettings.json" içinde "DefaultConnection"
        /// </summary>
        public static XECOMDbContext CreateDbContext(IConfiguration configuration)
        {
            var builder = new DbContextOptionsBuilder<XECOMDbContext>();
            builder.UseSqlServer(configuration.GetConnectionString("DefaultConnection"));
            return new XECOMDbContext(builder.Options);
        }
    }
}
