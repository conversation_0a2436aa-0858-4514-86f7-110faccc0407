﻿// XECOM_Client_CloudServer\Services\DtoMapper.cs
using System.Linq;
using XECOM_Client_CloudServer.Models;
using XECOM_Client_CloudServer.Models.DTOs;

namespace XECOM_Client_CloudServer.Services
{
    public static class DtoMapper
    {
        public static OrderDto ToDto(this IksOrder order)
        {
            if (order == null) return null;

            return new OrderDto
            {
                Id = order.Id,
                OrderNumber = order.OrderNumber,
                OrderedAt = order.OrderedAt, // Bu alan <PERSON>'da var.
                Deleted = order.Deleted,
                TotalPrice = order.TotalPrice,
                TotalFinalPrice = order.TotalFinalPrice,

                // BillingAddress DTO'ya çevrilip ekleniyor
                BillingAddress = order.BillingAddress?.ToDto(), // Null check önemli

                LineItems = order.OrderLineItems?
                                .Select(li => li.ToDto())
                                .ToList() ?? new List<OrderLineItemDto>(), // Null check

                Packages = order.OrderPackages?
                                .Select(p => p.ToDto())
                                .ToList() ?? new List<OrderPackageDto>() // Null check
            };
        }

        public static OrderLineItemDto ToDto(this IksOrderLineItem li)
        {
            if (li == null) return null;

            return new OrderLineItemDto
            {
                Id = li.Id,
                VariantSku = li.VariantSku,     // EKLENDİ
                VariantName = li.VariantName,   // EKLENDİ
                Quantity = li.Quantity,
                Price = li.Price,
                FinalPrice = li.FinalPrice
            };
        }

        public static OrderPackageDto ToDto(this IksOrderPackage pack)
        {
            if (pack == null) return null;

            return new OrderPackageDto
            {
                Id = pack.Id,
                Deleted = pack.Deleted,
                OrderPackageNumber = pack.OrderPackageNumber
            };
        }

        // IksAddress için yeni DTO mapper
        public static BillingAddressDto ToDto(this IksAddress address)
        {
            if (address == null) return null;

            return new BillingAddressDto
            {
                FirstName = address.FirstName,
                LastName = address.LastName,
                Company = address.Company,
                Phone = address.Phone,
                AddressLine1 = address.AddressLine1,
                AddressLine2 = address.AddressLine2,
                CityName = address.CityName,
                DistrictName = address.DistrictName,
                StateName = address.StateName,
                PostalCode = address.PostalCode,
                CountryName = address.CountryName,
                TaxOffice = address.TaxOffice,
                TaxNumber = address.TaxNumber,
                IdentityNumber = address.IdentityNumber
            };
        }
    }
}