﻿using Microsoft.EntityFrameworkCore;
using XECOM_Client_CloudServer.Models;
using System;

namespace XECOM_Client_CloudServer.Data
{
    public class XECOMDbContext : DbContext
    {
        public XECOMDbContext(DbContextOptions<XECOMDbContext> options) : base(options)
        {
            // ChangeTracker ayarları
            ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.TrackAll;
            ChangeTracker.AutoDetectChangesEnabled = true;
            ChangeTracker.LazyLoadingEnabled = false;
        }

        // Tabloların tanımlanması
        public DbSet<Product>? Products { get; set; }
        public DbSet<IksOrder> IksOrders { get; set; }
        public DbSet<IksOrderLineItem> IksOrderLineItems { get; set; }
        public DbSet<IksOrderPackage> IksOrderPackages { get; set; }
        public DbSet<IksAddress> IksAddresses { get; set; }
        public DbSet<SyncLog> SyncLogs { get; set; }


        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);

            // Query optimizasyonu için
            optionsBuilder.EnableSensitiveDataLogging(false);
            optionsBuilder.EnableDetailedErrors();
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // İlk uygulama alanları
            modelBuilder.Entity<Product>(entity =>
            {
                // Tablo konfigürasyonu
                entity.ToTable("Products_XONEsource_Urunler");
                entity.HasIndex(p => new { p.EAN, p.SKU_ID }).IsUnique(true);

                // Primary Key ve Identity konfigürasyonu
                entity.HasKey(e => e.ID);
                entity.Property(e => e.ID)
                    .UseIdentityColumn()
                    .IsRequired()
                    .ValueGeneratedOnAdd();

                // Unique indexler
                entity.HasIndex(p => p.SKU_ID)
                    .IsUnique()
                    .HasFilter("[SKU_ID] IS NOT NULL")
                    .HasDatabaseName("IX_Products_SKU_ID");

                entity.HasIndex(p => p.EAN)
                    .IsUnique()
                    .HasFilter("[EAN] IS NOT NULL")
                    .HasDatabaseName("IX_Products_EAN");

                // Zorunlu alanlar
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(500);

                entity.Property(e => e.Stock_Quantity)
                    .IsRequired()
                    .HasDefaultValue("0");

                entity.Property(e => e.UpdateDate)
                    .IsRequired()
                    .HasDefaultValueSql("GETUTCDATE()");

                entity.Property(e => e.RecordDate)
                    .HasDefaultValueSql("GETUTCDATE()");

                // String alan uzunluk kısıtlamaları
                entity.Property(e => e.SKU_ID).HasMaxLength(100);
                entity.Property(e => e.EAN).HasMaxLength(13);
                entity.Property(e => e.Category).HasMaxLength(200);
                entity.Property(e => e.AssortmentType).HasMaxLength(100);
                entity.Property(e => e.Brand).HasMaxLength(200);
                entity.Property(e => e.ModelID).HasMaxLength(100);
                entity.Property(e => e.Target_Age_Groups).HasMaxLength(100);
                entity.Property(e => e.Target_Genders).HasMaxLength(100);
                entity.Property(e => e.Season).HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(4000);

                // Material alanları için uzunluk kısıtlamaları
                entity.Property(e => e.Material_Upper_Material_Clothing1).HasMaxLength(200);
                entity.Property(e => e.Material_Upper_Material_Clothing2).HasMaxLength(200);
                entity.Property(e => e.Material_Upper_Material_Clothing3).HasMaxLength(200);
                entity.Property(e => e.Material_Upper_Material_Clothing4).HasMaxLength(200);

                // Media alanları için uzunluk kısıtlamaları
                entity.Property(e => e.Media1).HasMaxLength(1000);
                entity.Property(e => e.Media2).HasMaxLength(1000);
                entity.Property(e => e.Media3).HasMaxLength(1000);
                entity.Property(e => e.Media4).HasMaxLength(1000);
                entity.Property(e => e.Media5).HasMaxLength(1000);

                // Diğer alan kısıtlamaları
                entity.Property(e => e.Color_Code_Primary).HasMaxLength(50);
                entity.Property(e => e.Supplier_Color).HasMaxLength(100);
                entity.Property(e => e.Size).HasMaxLength(50);
                entity.Property(e => e.RegularPrice).HasMaxLength(50);
                entity.Property(e => e.Discount).HasMaxLength(50);
                entity.Property(e => e.Currency).HasMaxLength(10);
                entity.Property(e => e.LanguageCode).HasMaxLength(10);
                entity.Property(e => e.CargoDesi).HasMaxLength(50);
                entity.Property(e => e.DeliveryCosts).HasMaxLength(50);

                // Material oran alanları için uzunluk kısıtlamaları
                entity.Property(e => e.Material_Upper_Material_Clothing1_Ratio).HasMaxLength(50);
                entity.Property(e => e.Material_Upper_Material_Clothing2_Ratio).HasMaxLength(50);
                entity.Property(e => e.Material_Upper_Material_Clothing3_Ratio).HasMaxLength(50);
                entity.Property(e => e.Material_Upper_Material_Clothing4_Ratio).HasMaxLength(50);
            });

            
        }
    }
}