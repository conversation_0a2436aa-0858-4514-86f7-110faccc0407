﻿// XECOM_Client_CloudServer\Services\EncryptionService.cs
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json; // EncryptChanges ve DecryptChanges için
using Serilog;         // Global Serilog logger için (Log.Error)
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json.Serialization; // System.Text.Json.JsonSerializer için

namespace XECOM_Client_CloudServer.Services
{
    public static class EncryptionService
    {
        private static readonly System.Text.Json.JsonSerializerOptions SystemJsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            // Gerekirse diğer System.Text.Json ayarları eklenebilir
        };

        public static string Encrypt(string plainText, IConfiguration configuration)
        {
            if (string.IsNullOrEmpty(plainText))
            {
                Log.Warning("[EncryptionService-Client] Encrypt called with null or empty plainText.");
                return string.Empty; // veya null, uygulamanın beklentisine göre
            }

            var keyBase64 = configuration["Encryption:Key"];
            var ivBase64 = configuration["Encryption:IV"];

            if (string.IsNullOrEmpty(keyBase64) || string.IsNullOrEmpty(ivBase64))
            {
                Log.Error("[EncryptionService-Client] Encryption key or IV not found in configuration. Please check appsettings.json.");
                throw new InvalidOperationException("Encryption key or IV not found in configuration.");
            }

            byte[] key;
            byte[] iv;
            try
            {
                key = Convert.FromBase64String(keyBase64);
                iv = Convert.FromBase64String(ivBase64);
            }
            catch (FormatException ex)
            {
                Log.Error(ex, "[EncryptionService-Client] Encryption key or IV is not a valid Base64 string.");
                throw;
            }

            try
            {
                using var aes = Aes.Create();
                aes.Key = key;
                aes.IV = iv;
                aes.Padding = PaddingMode.PKCS7; // Sunucu ile aynı olmalı
                aes.Mode = CipherMode.CBC;       // Sunucu ile aynı olmalı (varsayılan genellikle CBC'dir)

                using var ms = new MemoryStream();
                using (var encryptor = aes.CreateEncryptor(aes.Key, aes.IV))
                using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                using (var sw = new StreamWriter(cs, Encoding.UTF8)) // UTF8 kullandığınızdan emin olun
                {
                    sw.Write(plainText);
                } // StreamWriter ve CryptoStream burada dispose edilir ve flushlanır

                return Convert.ToBase64String(ms.ToArray());
            }
            catch (CryptographicException ex)
            {
                Log.Error(ex, "[EncryptionService-Client] Cryptographic error during encryption.");
                throw;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "[EncryptionService-Client] Unexpected error during encryption.");
                throw;
            }
        }

        public static string Decrypt(string encryptedBase64, IConfiguration configuration)
        {
            if (string.IsNullOrEmpty(encryptedBase64))
            {
                Log.Warning("[EncryptionService-Client] Decrypt called with null or empty encryptedBase64 string.");
                return null;
            }

            var keyBase64 = configuration["Encryption:Key"];
            var ivBase64 = configuration["Encryption:IV"];

            if (string.IsNullOrEmpty(keyBase64) || string.IsNullOrEmpty(ivBase64))
            {
                Log.Error("[EncryptionService-Client] Encryption key or IV not found in configuration for decryption.");
                throw new InvalidOperationException("Encryption key or IV not found in configuration.");
            }

            byte[] key;
            byte[] iv;
            try
            {
                key = Convert.FromBase64String(keyBase64);
                iv = Convert.FromBase64String(ivBase64);
            }
            catch (FormatException ex)
            {
                Log.Error(ex, "[EncryptionService-Client] Encryption key or IV is not a valid Base64 string for decryption.");
                throw;
            }

            try
            {
                using var aes = Aes.Create();
                aes.Key = key;
                aes.IV = iv;
                aes.Padding = PaddingMode.PKCS7; // Sunucu ile aynı olmalı
                aes.Mode = CipherMode.CBC;       // Sunucu ile aynı olmalı

                byte[] cipherBytes;
                try
                {
                    cipherBytes = Convert.FromBase64String(encryptedBase64);
                }
                catch (FormatException ex)
                {
                    Log.Error(ex, "[EncryptionService-Client] Decryption failed. Encrypted data is not a valid Base64 string. Data (first 50 chars): {Data}", encryptedBase64.Length > 50 ? encryptedBase64.Substring(0, 50) : encryptedBase64);
                    throw;
                }

                using var ms = new MemoryStream();
                using (var decryptor = aes.CreateDecryptor(aes.Key, aes.IV))
                using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Write))
                {
                    cs.Write(cipherBytes, 0, cipherBytes.Length);
                } // CryptoStream burada dispose edilir ve flushlanır

                return Encoding.UTF8.GetString(ms.ToArray());
            }
            catch (CryptographicException ex) // Padding hataları vb.
            {
                Log.Error(ex, "[EncryptionService-Client] Cryptographic error during decryption. Incorrect padding, key, or IV possible. Data (first 50 chars): {Data}", encryptedBase64.Length > 50 ? encryptedBase64.Substring(0, 50) : encryptedBase64);
                throw;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "[EncryptionService-Client] Unexpected error during decryption.");
                throw;
            }
        }

        public static string EncryptData<T>(T[] data, IConfiguration configuration)
        {
            try
            {
                var jsonData = System.Text.Json.JsonSerializer.Serialize(data, SystemJsonOptions);
                return Encrypt(jsonData, configuration);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "[EncryptionService-Client] EncryptData (System.Text.Json) error.");
                throw;
            }
        }

        public static string EncryptChanges<T>(T[] changes, IConfiguration configuration)
        {
            try
            {
                var jsonData = JsonConvert.SerializeObject(changes); // Newtonsoft.Json
                return Encrypt(jsonData, configuration);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "[EncryptionService-Client] EncryptChanges (Newtonsoft.Json) error.");
                throw;
            }
        }

        public static T[] DecryptData<T>(string encryptedBase64, IConfiguration configuration)
        {
            try
            {
                var decryptedJson = Decrypt(encryptedBase64, configuration);
                if (decryptedJson == null) return null;
                return System.Text.Json.JsonSerializer.Deserialize<T[]>(decryptedJson, SystemJsonOptions);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "[EncryptionService-Client] DecryptData (System.Text.Json) error.");
                throw; // veya null döndür, uygulamanın hata yönetimine bağlı
            }
        }

        public static T[] DecryptChanges<T>(string encryptedBase64, IConfiguration configuration)
        {
            try
            {
                var decryptedJson = Decrypt(encryptedBase64, configuration);
                if (decryptedJson == null) return null;
                return JsonConvert.DeserializeObject<T[]>(decryptedJson); // Newtonsoft.Json
            }
            catch (Exception ex)
            {
                Log.Error(ex, "[EncryptionService-Client] DecryptChanges (Newtonsoft.Json) error.");
                throw; // veya null döndür
            }
        }
    }
}