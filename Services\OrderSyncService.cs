﻿// XECOM_Client_CloudServer\Services\OrderSyncService.cs
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.EntityFrameworkCore; // Bu using önemli
using Microsoft.Extensions.Configuration;
using Serilog;
using System; // DateTime ve Any için
using System.Linq; // Select ve ToList için
using System.Text.Json;
using System.Threading.Tasks; // async/await için
using XECOM_Client_CloudServer.Data;
using XECOM_Client_CloudServer.Models;
using XECOM_Client_CloudServer.Models.DTOs;


namespace XECOM_Client_CloudServer.Services
{
    public static class OrderSyncService
    {
        public static async Task SendOrdersToLocalServerAsync(
        IConfiguration configuration,
        XECOMDbContext dbContext,
        HubConnection hubConnection)
        {
            try
            {
                Log.Information("[OrderSyncService] Sipariş senkronizasyonu başlıyor...");

                // 1) EF'den siparişleri çek
                // GÜNCELLEME: BillingAddress'i de Include et
                var recentOrders = await dbContext.IksOrders
                    .Include(o => o.BillingAddress) // BU SATIR EKLENDİ/GÜNCELLENDİ
                    .Include(o => o.OrderLineItems)
                    .Include(o => o.OrderPackages)
                    .Where(o => o.OrderedAt > DateTime.UtcNow.AddDays(-1) && o.Deleted != true) // Sadece silinmemişler
                    .OrderByDescending(o => o.OrderedAt) // En yeniden eskiye doğru
                    .Take(100) // Güvenlik için bir seferde maksimum 100 sipariş alalım (opsiyonel)
                    .ToListAsync();

                if (!recentOrders.Any())
                {
                    Log.Information("[OrderSyncService] Gönderilecek sipariş yok.");
                    return;
                }

                Log.Information("[OrderSyncService] {count} adet sipariş bulundu, gönderiliyor...", recentOrders.Count);

                // 2) Entity -> DTO (DtoMapper artık BillingAddress, VariantSku, VariantName içerecek)
                var orderDtos = recentOrders.Select(o => o.ToDto()).ToList();

                // DTO'ları kontrol et (özellikle BillingAddress null mu diye)
                foreach (var dto in orderDtos)
                {
                    if (dto.BillingAddress == null)
                    {
                        Log.Warning("[OrderSyncService] OrderNumber {OrderNumber} için BillingAddress DTO'su null. IksOrder.BillingAddressId: {BillingAddressId}", dto.OrderNumber, recentOrders.FirstOrDefault(ro => ro.OrderNumber == dto.OrderNumber)?.BillingAddressId);
                    }
                    if (dto.LineItems.Any(li => string.IsNullOrWhiteSpace(li.VariantSku)))
                    {
                        Log.Warning("[OrderSyncService] OrderNumber {OrderNumber} için VariantSku'su boş olan kalem(ler) var.", dto.OrderNumber);
                    }
                }

                // 3) JSON serileştirme (DTO'da cycle yok, hata da yok)
                var jsonData = JsonSerializer.Serialize(orderDtos, new JsonSerializerOptions { WriteIndented = true }); // Geliştirme için WriteIndented

                // İsteğe bağlı: Oluşan JSON'ı logla (çok uzun olabilir, dikkatli kullan)
                // Log.Debug("[OrderSyncService] Gönderilecek JSON (ilk 500 char): {JsonData}", jsonData.Length > 500 ? jsonData.Substring(0, 500) : jsonData);

                // 4) AES şifreleme
                var encryptedData = EncryptionService.Encrypt(jsonData, configuration);

                // 5) SignalR ile Local'e gönder
                Log.Information("[OrderSyncService] SignalR ile ana sunucuya 'ReceiveOrders' metoduna şifreli veri gönderiliyor...");
                await hubConnection.InvokeAsync("ReceiveOrders", encryptedData);

                Log.Information("[OrderSyncService] {count} adet sipariş başarıyla ana sunucuya gönderildi.", orderDtos.Count);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "[OrderSyncService] Sipariş gönderme sırasında hata: {message}", ex.Message);
                // Hata durumunda, hatanın detayını da InvokeAsync ile ana sunucuya bildirebiliriz (opsiyonel)
                // await hubConnection.InvokeAsync("NotifyError", $"OrderSyncService Error: {ex.Message}");
            }
        }
    }
}