﻿// XECOM_Client_CloudServer\Services\SignalRService.cs
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using XECOM_Client_CloudServer.Data;
using XECOM_Client_CloudServer.Models;
using Newtonsoft.Json; // JArray ve JToken için önemli
using Newtonsoft.Json.Linq; // JArray ve JToken için önemli

namespace XECOM_Client_CloudServer.Services
{
    public static class SignalRService
    {
        private static readonly SemaphoreSlim _fileWriteSemaphore = new SemaphoreSlim(1, 1);

        public static async Task<HubConnection> InitializeConnectionAsync(IConfiguration configuration, Serilog.ILogger logger)
        {
            var serverUrl = configuration["ServerUrl"];
            if (string.IsNullOrEmpty(serverUrl))
            {
                logger.Error("[SignalRService] ServerUrl is not configured in appsettings.json. Cannot initialize SignalR connection.");
                throw new InvalidOperationException("ServerUrl is not configured. Please check appsettings.json.");
            }
            var hubUrl = serverUrl.TrimEnd('/') + "/dataSyncHub";
            logger.Information("[SignalRService] Attempting to connect to SignalR Hub at: {HubUrl}", hubUrl);

            try
            {
                var hubConnection = new HubConnectionBuilder()
                    .WithUrl(hubUrl)
                    .WithAutomaticReconnect(new[]
                    {
                        TimeSpan.Zero, // Hemen yeniden dene
                        TimeSpan.FromSeconds(2),
                        TimeSpan.FromSeconds(10),
                        TimeSpan.FromSeconds(30)
                    })
                    .Build();

                hubConnection.Reconnecting += error =>
                {
                    logger.Warning(error, "[SignalRService] Connection lost. Attempting to reconnect to {HubUrl}...", hubUrl);
                    return Task.CompletedTask;
                };
                hubConnection.Reconnected += connectionId =>
                {
                    logger.Information("[SignalRService] Connection re-established successfully to {HubUrl}. ConnectionId: {ConnectionId}", hubUrl, connectionId);
                    return Task.CompletedTask;
                };
                hubConnection.Closed += error =>
                {
                    if (error != null)
                    {
                        logger.Error(error, "[SignalRService] Connection closed with error to {HubUrl}. Will attempt to reconnect if WithAutomaticReconnect is configured.", hubUrl);
                    }
                    else
                    {
                        logger.Information("[SignalRService] Connection closed gracefully to {HubUrl}.", hubUrl);
                    }
                    return Task.CompletedTask;
                };

                // Bağlantıyı CancellationToken ile başlatmak daha iyi bir pratiktir,
                // özellikle uygulamanın düzgün kapatılması senaryolarında.
                // Örneğin, Program.cs'ten gelen bir CancellationToken buraya verilebilir.
                // Şimdilik CancellationToken.None kullanıyorum (veya hiç vermiyorum).
                await hubConnection.StartAsync();
                logger.Information("[SignalRService] SignalR connection started successfully. State: {State}, ConnectionId: {ConnectionId}", hubConnection.State, hubConnection.ConnectionId);

                return hubConnection;
            }
            catch (Exception ex)
            {
                logger.Error(ex, "[SignalRService] Error initializing SignalR connection to {HubUrl}", hubUrl);
                throw;
            }
        }

        public static async Task HandleSignalREventsAsync(
            HubConnection hubConnection,
            IConfiguration configuration,
            Serilog.ILogger logger)
        {
            if (hubConnection == null)
            {
                logger.Error("[SignalRService] HandleSignalREventsAsync called with a null HubConnection. Cannot subscribe to events.");
                return;
            }

            logger.Information("[SignalRService] Subscribing to 'ReceiveData' event from server for HubConnection: {ConnectionId}", hubConnection.ConnectionId);

            Func<string, Task> receiveDataHandler = async (encryptedData) =>
            {
                string sessionId = Guid.NewGuid().ToString().Substring(0, 8);
                logger.Information("[SignalRService-ReceiveDataHandler] 'ReceiveData' triggered [Session: {SessionId}], encrypted data length: {Length}",
                    sessionId, encryptedData?.Length ?? 0);

                var serviceProvider = new ServiceCollection()
                    .AddDbContext<XECOMDbContext>(options =>
                        options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")))
                    .BuildServiceProvider();

                using var scope = serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<XECOMDbContext>();

                string decryptedJson = null;
                try
                {
                    if (string.IsNullOrEmpty(encryptedData))
                    {
                        logger.Warning("[{SessionId}] Received null or empty encryptedData in 'ReceiveData' handler.", sessionId);
                        return;
                    }

                    decryptedJson = EncryptionService.Decrypt(encryptedData, configuration);
                    if (string.IsNullOrEmpty(decryptedJson))
                    {
                        logger.Error("[{SessionId}] Decryption resulted in null or empty JSON string in 'ReceiveData' handler.", sessionId);
                        return;
                    }

                    decryptedJson = CleanJsonString(decryptedJson);

                    logger.Information("[{SessionId}] Decryption and cleaning successful in 'ReceiveData', JSON length: {Length}, first 100 chars: {JsonPreview}",
                        sessionId, decryptedJson.Length,
                        decryptedJson.Length > 100 ? decryptedJson.Substring(0, 100) : decryptedJson);

                    await _fileWriteSemaphore.WaitAsync();
                    try
                    {
                        string logDirectory = Path.Combine(Directory.GetCurrentDirectory(), "logs");
                        Directory.CreateDirectory(logDirectory);
                        string filename = Path.Combine(logDirectory, $"last_received_data_{sessionId}_{DateTime.Now:yyyyMMdd_HHmmss}.json");
                        await File.WriteAllTextAsync(filename, decryptedJson);
                        logger.Information("[{SessionId}] JSON data from 'ReceiveData' saved to file: {Filename}", sessionId, filename);
                    }
                    catch (Exception fileEx)
                    {
                        logger.Warning(fileEx, "[{SessionId}] Could not save JSON data from 'ReceiveData' to file", sessionId);
                    }
                    finally
                    {
                        _fileWriteSemaphore.Release();
                    }
                }
                catch (Exception exDecrypt)
                {
                    logger.Error(exDecrypt, "[{SessionId}] Decryption error in 'ReceiveData' handler. Encrypted data preview (first 50 chars): {EncryptedPreview}",
                        sessionId, encryptedData?.Length > 50 ? encryptedData.Substring(0, 50) : encryptedData ?? "NULL_DATA");
                    return;
                }

                JArray productsArray;
                try
                {
                    productsArray = JArray.Parse(decryptedJson);
                    logger.Information("[{SessionId}] JSON from 'ReceiveData' parsed as JArray, product count: {Count}", sessionId, productsArray.Count);

                    if (productsArray.Count > 0 && productsArray[0] != null)
                    {
                        logger.Information("[{SessionId}] First product details from 'ReceiveData': ID={ID}, SKU_ID={SKU_ID}, EAN={EAN}, Name={Name}",
                            sessionId, productsArray[0]["ID"], productsArray[0]["SKU_ID"],
                            productsArray[0]["EAN"], productsArray[0]["Name"]);
                    }
                }
                catch (JsonReaderException jsonEx)
                {
                    logger.Error(jsonEx, "[{SessionId}] JSON parse error (Newtonsoft.Json) in 'ReceiveData'. Decrypted JSON preview (first 200 chars): {JsonPreview}",
                        sessionId, decryptedJson.Length > 200 ? decryptedJson.Substring(0, 200) : decryptedJson);
                    return;
                }
                catch (Exception exGenJson)
                {
                    logger.Error(exGenJson, "[{SessionId}] Unexpected error during JSON parsing in 'ReceiveData'.", sessionId);
                    return;
                }

                if (productsArray == null || productsArray.Count == 0)
                {
                    logger.Warning("[{SessionId}] Product list from 'ReceiveData' is null or empty after parsing. No products to process.", sessionId);
                    return;
                }

                logger.Information("[{SessionId}] Starting database operations for {Count} products from 'ReceiveData'...", sessionId, productsArray.Count);
                int newCount = 0;
                int updatedCount = 0;
                int errorCount = 0;
                int skipCount = 0;
                int batchSize = configuration.GetValue<int>("ProductSyncBatchSize", 100);

                foreach (JToken productToken in productsArray)
                {
                    if (productToken == null)
                    {
                        logger.Warning("[{SessionId}] Null product token encountered in 'ReceiveData' array. Skipping.", sessionId);
                        skipCount++;
                        continue;
                    }
                    try
                    {
                        string skuId = productToken["SKU_ID"]?.ToString();
                        string ean = productToken["EAN"]?.ToString();

                        if (string.IsNullOrEmpty(skuId) || string.IsNullOrEmpty(ean))
                        {
                            logger.Warning("[{SessionId}] Skipping product with null/empty SKU_ID or EAN from 'ReceiveData'. Product Token ID (if exists): {ProductId}",
                                sessionId, productToken["ID"]);
                            skipCount++;
                            continue;
                        }

                        var cloudProduct = ConvertToCloudProduct(productToken);
                        var existing = await dbContext.Products
                                          .FirstOrDefaultAsync(x => x.EAN == cloudProduct.EAN && x.SKU_ID == cloudProduct.SKU_ID);

                        if (existing == null)
                        {
                            cloudProduct.UpdateDate = DateTime.UtcNow;
                            cloudProduct.RecordDate = DateTime.UtcNow;
                            EnsureRequiredFieldsNotNull(cloudProduct);
                            dbContext.Products.Add(cloudProduct);
                            newCount++;
                        }
                        else
                        {
                            existing.Name = cloudProduct.Name;
                            existing.Description = cloudProduct.Description ?? existing.Description;
                            existing.Brand = cloudProduct.Brand ?? existing.Brand;
                            existing.Stock_Quantity = cloudProduct.Stock_Quantity;
                            existing.Category = cloudProduct.Category ?? existing.Category;
                            existing.UpdateDate = DateTime.UtcNow;
                            existing.AssortmentType = cloudProduct.AssortmentType ?? existing.AssortmentType;
                            existing.ModelID = cloudProduct.ModelID ?? existing.ModelID;
                            existing.Target_Age_Groups = cloudProduct.Target_Age_Groups ?? existing.Target_Age_Groups;
                            existing.Target_Genders = cloudProduct.Target_Genders ?? existing.Target_Genders;
                            existing.Season = cloudProduct.Season ?? existing.Season;
                            UpdateMaterialFields(existing, cloudProduct);
                            UpdateMediaFields(existing, cloudProduct);
                            UpdateOtherFields(existing, cloudProduct);
                            dbContext.Products.Update(existing);
                            updatedCount++;
                        }

                        if ((newCount + updatedCount) > 0 && (newCount + updatedCount) % batchSize == 0)
                        {
                            logger.Information("[{SessionId}] Reached batch size ({BatchSize}). Saving changes for products from 'ReceiveData'...", sessionId, batchSize);
                            await dbContext.SaveChangesAsync();
                            logger.Information("[{SessionId}] Batch changes saved.", sessionId);
                        }
                    }
                    catch (Exception productEx)
                    {
                        errorCount++;
                        logger.Error(productEx, "[{SessionId}] Error processing a product from 'ReceiveData'. Product token preview: {ProductTokenPreview}",
                            sessionId, productToken?.ToString(Formatting.None).Substring(0, Math.Min(productToken.ToString(Formatting.None).Length, 200)) ?? "NULL_TOKEN");
                    }
                }

                if (dbContext.ChangeTracker.HasChanges())
                {
                    logger.Information("[{SessionId}] Saving final pending changes for products from 'ReceiveData' ({ChangeCount} changes)...", sessionId, dbContext.ChangeTracker.Entries().Count(e => e.State != EntityState.Unchanged));
                    await dbContext.SaveChangesAsync();
                    logger.Information("[{SessionId}] Final pending changes saved.", sessionId);
                }

                logger.Information("[{SessionId}] Database operations for 'ReceiveData' completed. New: {NewCount}, Updated: {UpdatedCount}, Errors: {ErrorCount}, Skipped: {SkipCount}",
                    sessionId, newCount, updatedCount, errorCount, skipCount);

                try
                {
                    var totalProducts = await dbContext.Products.CountAsync();
                    logger.Information("[{SessionId}] Total products in database after 'ReceiveData' operation: {Count}", sessionId, totalProducts);
                }
                catch (Exception countEx) { logger.Warning(countEx, "[{SessionId}] Could not get total product count after operation.", sessionId); }

                if (DateTime.Now.Hour % 3 == 0 && DateTime.Now.Minute < 5)
                {
                    logger.Information("[{SessionId}] Attempting to cleanup old debug files...", sessionId);
                    CleanupOldDebugFiles(logger);
                }
            }; // <<< NOKTALI VİRGÜL BURADA OLMALI (lambda atamasının sonu)

            hubConnection.On<string>("ReceiveData", receiveDataHandler);
            // Sunucudan "ReceiveChanges" gibi başka olaylar da geliyorsa, onlar için de handler'lar buraya eklenebilir.
            // Örneğin: hubConnection.On<string>("ReceiveChanges", receiveChangesHandler);

            logger.Information("[SignalRService] Event handler for 'ReceiveData' subscribed on HubConnection: {ConnectionId}", hubConnection.ConnectionId);
        }

        // Yardımcı Metodlar (Bunlar sizin tarafınızdan sağlanmıştı ve aynı kalmalı)
        private static void UpdateMaterialFields(Product existing, Product cloudProduct)
        {
            existing.Material_Upper_Material_Clothing1 = cloudProduct.Material_Upper_Material_Clothing1 ?? existing.Material_Upper_Material_Clothing1;
            existing.Material_Upper_Material_Clothing1_Ratio = cloudProduct.Material_Upper_Material_Clothing1_Ratio ?? existing.Material_Upper_Material_Clothing1_Ratio;
            existing.Material_Upper_Material_Clothing2 = cloudProduct.Material_Upper_Material_Clothing2 ?? existing.Material_Upper_Material_Clothing2;
            existing.Material_Upper_Material_Clothing2_Ratio = cloudProduct.Material_Upper_Material_Clothing2_Ratio ?? existing.Material_Upper_Material_Clothing2_Ratio;
            existing.Material_Upper_Material_Clothing3 = cloudProduct.Material_Upper_Material_Clothing3 ?? existing.Material_Upper_Material_Clothing3;
            existing.Material_Upper_Material_Clothing3_Ratio = cloudProduct.Material_Upper_Material_Clothing3_Ratio ?? existing.Material_Upper_Material_Clothing3_Ratio;
            existing.Material_Upper_Material_Clothing4 = cloudProduct.Material_Upper_Material_Clothing4 ?? existing.Material_Upper_Material_Clothing4;
            existing.Material_Upper_Material_Clothing4_Ratio = cloudProduct.Material_Upper_Material_Clothing4_Ratio ?? existing.Material_Upper_Material_Clothing4_Ratio;
        }

        private static void UpdateMediaFields(Product existing, Product cloudProduct)
        {
            existing.Media1 = cloudProduct.Media1 ?? existing.Media1;
            existing.Media2 = cloudProduct.Media2 ?? existing.Media2;
            existing.Media3 = cloudProduct.Media3 ?? existing.Media3;
            existing.Media4 = cloudProduct.Media4 ?? existing.Media4;
            existing.Media5 = cloudProduct.Media5 ?? existing.Media5;
        }

        private static void UpdateOtherFields(Product existing, Product cloudProduct)
        {
            existing.Color_Code_Primary = cloudProduct.Color_Code_Primary ?? existing.Color_Code_Primary;
            existing.Supplier_Color = cloudProduct.Supplier_Color ?? existing.Supplier_Color;
            existing.Size = cloudProduct.Size ?? existing.Size;
            existing.RegularPrice = cloudProduct.RegularPrice ?? existing.RegularPrice;
            existing.Discount = cloudProduct.Discount ?? existing.Discount;
            existing.Currency = cloudProduct.Currency ?? existing.Currency;
        }

        private static void CleanupOldDebugFiles(Serilog.ILogger logger)
        {
            try
            {
                string logDirectory = Path.Combine(Directory.GetCurrentDirectory(), "logs");
                if (!Directory.Exists(logDirectory))
                {
                    logger.Debug("[CleanupOldDebugFiles] Log directory '{LogDirectory}' does not exist. Nothing to cleanup.", logDirectory);
                    return;
                }

                var files = Directory.GetFiles(logDirectory, "last_received_data_*.json");
                int cleanedCount = 0;
                foreach (var file in files)
                {
                    try
                    {
                        if (File.GetCreationTime(file) < DateTime.Now.AddDays(-1))
                        {
                            File.Delete(file);
                            cleanedCount++;
                            logger.Debug("[CleanupOldDebugFiles] Deleted old debug file: {File}", file);
                        }
                    }
                    catch (Exception exDelete)
                    {
                        logger.Warning(exDelete, "[CleanupOldDebugFiles] Could not delete old debug file: {File}", file);
                    }
                }
                if (cleanedCount > 0)
                {
                    logger.Information("[CleanupOldDebugFiles] Cleaned up {CleanedCount} old debug files from '{LogDirectory}'.", cleanedCount, logDirectory);
                }
                else
                {
                    logger.Debug("[CleanupOldDebugFiles] No old debug files found to cleanup in '{LogDirectory}'.", logDirectory);
                }
            }
            catch (Exception ex)
            {
                logger.Warning(ex, "[CleanupOldDebugFiles] Error during cleanup process.");
            }
        }

        private static Product ConvertToCloudProduct(JToken productToken)
        {
            var product = new Product
            {
                Name = productToken["Name"]?.ToString() ?? "Unnamed Product",
                SKU_ID = productToken["SKU_ID"]?.ToString() ?? string.Empty,
                EAN = productToken["EAN"]?.ToString() ?? string.Empty,
                Category = productToken["Category"]?.ToString(),
                AssortmentType = productToken["Assortment_type"]?.ToString(),
                Brand = productToken["Brand"]?.ToString(),
                ModelID = productToken["Model_ID"]?.ToString(),
                Target_Age_Groups = productToken["Target_Age_Groups"]?.ToString(),
                Target_Genders = productToken["Target_Genders"]?.ToString(),
                Season = productToken["Season"]?.ToString(),
                Description = productToken["Description"]?.ToString(),
                Material_Upper_Material_Clothing1 = productToken["Material_Upper_Material_Clothing1"]?.ToString(),
                Material_Upper_Material_Clothing1_Ratio = productToken["Material_Upper_Material_Clothing1_Ratio"]?.ToString(),
                Material_Upper_Material_Clothing2 = productToken["Material_Upper_Material_Clothing2"]?.ToString(),
                Material_Upper_Material_Clothing2_Ratio = productToken["Material_Upper_Material_Clothing2_Ratio"]?.ToString(),
                Material_Upper_Material_Clothing3 = productToken["Material_Upper_Material_Clothing3"]?.ToString(),
                Material_Upper_Material_Clothing3_Ratio = productToken["Material_Upper_Material_Clothing3_Ratio"]?.ToString(),
                Material_Upper_Material_Clothing4 = productToken["Material_Upper_Material_Clothing4"]?.ToString(),
                Material_Upper_Material_Clothing4_Ratio = productToken["Material_Upper_Material_Clothing4_Ratio"]?.ToString(),
                Media1 = productToken["Media1_URL"]?.ToString(),
                Media2 = productToken["Media2_URL"]?.ToString(),
                Media3 = productToken["Media3_URL"]?.ToString(),
                Media4 = productToken["Media4_URL"]?.ToString(),
                Media5 = productToken["Media5_URL"]?.ToString(),
                Color_Code_Primary = productToken["Color_Code_Primary"]?.ToString(),
                Supplier_Color = productToken["Supplier_Color"]?.ToString(),
                Size = productToken["Size"]?.ToString(),
                Stock_Quantity = productToken["Stock_Quantity"]?.ToString() ?? "0",
                RegularPrice = productToken["Regular_Price"]?.ToString(),
                Discount = productToken["Discount_Price"]?.ToString(),
                Currency = productToken["Currency"]?.ToString(),
                UpdateDate = DateTime.UtcNow, // Add/Update sırasında set ediliyor
                RecordDate = DateTime.UtcNow // Add sırasında set ediliyor
            };
            return product;
        }

        private static void EnsureRequiredFieldsNotNull(Product product)
        {
            product.Name = string.IsNullOrEmpty(product.Name) ? "Unknown Product Name" : product.Name;
            // SKU_ID ve EAN için boşsa geçici değer atama yerine, loglayıp atlamak daha iyi olabilir (yukarıda yapılıyor).
            // Ancak zorunluysa ve bir şekilde devam etmesi gerekiyorsa, geçici ID ataması bir seçenek olabilir.
            // Şimdilik loglama ve atlama (yukarıdaki foreach içinde) daha iyi.
            // product.SKU_ID = string.IsNullOrEmpty(product.SKU_ID) ? Guid.NewGuid().ToString().Substring(0,8) : product.SKU_ID;
            // product.EAN = string.IsNullOrEmpty(product.EAN) ? Guid.NewGuid().ToString().Substring(0,13) : product.EAN;
            product.Stock_Quantity = string.IsNullOrEmpty(product.Stock_Quantity) ? "0" : product.Stock_Quantity;
            product.Brand = product.Brand ?? string.Empty;
            product.Description = product.Description ?? string.Empty;
            product.Category = product.Category ?? string.Empty;
            product.AssortmentType = product.AssortmentType ?? string.Empty;
        }

        private static string CleanJsonString(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;
            if (input.StartsWith("\uFEFF")) // UTF-8 BOM
            {
                input = input.Substring(1);
            }
            // Diğer olası BOM'lar veya istenmeyen karakterler için daha kapsamlı temizlik eklenebilir.
            return input.Trim();
        }
    }
}