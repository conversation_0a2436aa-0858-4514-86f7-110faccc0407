﻿// Services/SyncLogService.cs
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Linq;
using XECOM_Client_CloudServer.Data;
using XECOM_Client_CloudServer.Models;

namespace XECOM_Client_CloudServer.Services
{
    public static class SyncLogService
    {
        // Varsa bir sync kaydı getir, yoksa oluştur
        public static DateTime GetLastSyncTime(XECOMDbContext dbContext, string entityName)
        {
            var log = dbContext.SyncLogs
                .FirstOrDefault(l => l.EntityName == entityName);
            if (log == null)
            {
                // Yoksa çok eski bir tarih dön
                return DateTime.MinValue;
            }
            return log.LastSyncTime;
        }

        // Sync zamanı güncelle
        public static void SetLastSyncTime(XECOMDbContext dbContext, string entityName, DateTime syncTime)
        {
            var log = dbContext.SyncLogs
                .FirstOrDefault(l => l.EntityName == entityName);

            if (log == null)
            {
                log = new SyncLog
                {
                    EntityName = entityName,
                    LastSyncTime = syncTime
                };
                dbContext.SyncLogs.Add(log);
            }
            else
            {
                log.LastSyncTime = syncTime;
            }

            dbContext.SaveChanges();
            Log.Information($"SyncLog güncellendi: {entityName} - {syncTime}");
        }
    }
}
