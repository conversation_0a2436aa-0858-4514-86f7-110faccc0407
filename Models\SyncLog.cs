﻿// Models/SyncLog.cs
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System;

namespace XECOM_Client_CloudServer.Models
{
    [Table("SyncLogs")]
    public class SyncLog
    {
        [Key]
        public int Id { get; set; }

        [Column("EntityName")]
        [Required]
        public string EntityName { get; set; }

        [Column("LastSyncTime")]
        public DateTime LastSyncTime { get; set; }
    }
}
