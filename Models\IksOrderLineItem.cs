﻿// Models/Orders/IksOrderLineItem.cs
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XECOM_Client_CloudServer.Models
{
    [Table("iks_OrderLineItems")]
    public class IksOrderLineItem
    {
        [Key]
        public long Id { get; set; }

        public string IkasId { get; set; }

        // EF Core ForeignKey
        [ForeignKey(nameof(Order))]
        public long OrderId { get; set; }

        public int? Quantity { get; set; }
        public decimal? Price { get; set; }
        public decimal? FinalPrice { get; set; }
        public string VariantName { get; set; }
        public string VariantSku { get; set; }

        // Navigation property
        public virtual IksOrder Order { get; set; }
    }
}
