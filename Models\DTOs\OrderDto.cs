﻿using System;
using System.Collections.Generic;

namespace XECOM_Client_CloudServer.Models.DTOs
{
    /// <summary>
    /// <PERSON><PERSON><PERSON><PERSON><PERSON> referans yok:
    /// IksOrderLineItemDto ve IksOrderPackageDto'larda "OrderDto" da yoktur.
    /// </summary>
    public class OrderDto
    {
        public long Id { get; set; }
        public string OrderNumber { get; set; }
        public DateTime? OrderedAt { get; set; }
        public bool? Deleted { get; set; } // IksOrder'dan geliyor

        // EKLENEN ALANLAR (Netsis için gerekli)
        public decimal? TotalPrice { get; set; }        // IksOrder'dan
        public decimal? TotalFinalPrice { get; set; }   // IksOrder'dan
        public BillingAddressDto BillingAddress { get; set; }

        public List<OrderLineItemDto> LineItems { get; set; } = new List<OrderLineItemDto>();
        public List<OrderPackageDto> Packages { get; set; } = new List<OrderPackageDto>();
    }
}

