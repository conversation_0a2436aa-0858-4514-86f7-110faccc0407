{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.;Database=XECOM;User Id=sa;Password=********;Connect Timeout=30;Encrypt=True;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;",
    //"DefaultConnection": "Server=.;Database=XECOM;User Id=sa;Password=*******************;Connect Timeout=30;Encrypt=True;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;"
  },
  "Encryption": {
    "Key": "BwmKp3kZjCm2FJkzEmXz/RlV9V7I9mxIvF9ZtbHtFa0=", // SUNUCU İLE AYNI OLMALI
    "IV": "Q9G5V/Lm+ZtJ2HVFZVtWFA==" // SUNUCU İLE AYNI OLMALI
  },
  "UseHttps": false, // Sunucu HTTP ise bu da false olmalı
  "UseChangeTracking": false,
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug", // Geliştirme sırasında Debug iyidir
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/client_log-.txt", // İstemci için ayrı log dosyası
          "rollingInterval": "Day"
        }
      }
    ]
  },
  "ServerUrl": "http://localhost:5280", // <<< ANAHTAR DEĞİŞİKLİK: SUNUCUNUN PORTU
  "IkasApi": { // Bu ayarlar Netsis entegrasyonuyla doğrudan ilgili değil, olduğu gibi kalabilir
    "StoreName": "storename",
    "BaseUrl": "https://nordbron.myikas.com/api",
    "ClientId": "35e853f1-401f-4d46-9827-c741cd56aec2",
    "ClientSecret": "s_5KQiEsKIg84L2gdrca1rWg5829710aa029754a7cad6986900ef03f15"
  }
}











//{
//  "ConnectionStrings": {
//    "DefaultConnection": "Server=.;Database=XECOM;User Id=sa;Password=********;Connect Timeout=30;Encrypt=True;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;"
//    //"DefaultConnection": "Server=.;Database=XECOM;User Id=sa;Password=*******************;Connect Timeout=30;Encrypt=True;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;"
//  },
//  "Encryption": {
//    "Key": "BwmKp3kZjCm2FJkzEmXz/RlV9V7I9mxIvF9ZtbHtFa0=",
//    "IV": "Q9G5V/Lm+ZtJ2HVFZVtWFA=="
//  },
//  "UseHttps": false,
//  "UseChangeTracking": false,
//  "Serilog": {
//    "MinimumLevel": {
//      "Default": "Debug",
//      "Override": {
//        "Microsoft": "Warning",
//        "System": "Warning"
//      }
//    },
//    "WriteTo": [
//      {
//        "Name": "Console"
//      },
//      {
//        "Name": "File",
//        "Args": {
//          "path": "logs/log-.txt",
//          "rollingInterval": "Day"
//        }
//      }
//    ]
//  },
//  "ServerUrl": "http://localhost:5279",
//  "IkasApi": {
//    "StoreName": "storename",
//    "BaseUrl": "https://nordbron.myikas.com/api",
//    "ClientId": "35e853f1-401f-4d46-9827-c741cd56aec2",
//    "ClientSecret": "s_5KQiEsKIg84L2gdrca1rWg5829710aa029754a7cad6986900ef03f15"
//  }

//}
