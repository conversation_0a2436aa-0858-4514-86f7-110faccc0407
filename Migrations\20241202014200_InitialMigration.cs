﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace XECOM_Client_CloudServer.Migrations
{
    /// <inheritdoc />
    public partial class InitialMigration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "iks_Customers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CustomerId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Phone = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_iks_Customers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "iks_Products",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    EAN = table.Column<string>(type: "nvarchar(13)", maxLength: 13, nullable: false),
                    SKU = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    BrandId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    BrandName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false),
                    ShortDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Weight = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TotalStock = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    MaxQuantityPerCart = table.Column<int>(type: "int", nullable: true),
                    ProductType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProductOptionSetId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProductVolumeDiscountId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    SalesChannelIds = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    HiddenSalesChannelIds = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CategoryIds = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TagIds = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_iks_Products", x => x.Id);
                });

            //migrationBuilder.CreateTable(
            //    name: "Products_XONEsource_Urunler",
            //    columns: table => new
            //    {
            //        ID = table.Column<long>(type: "bigint", nullable: false)
            //            .Annotation("SqlServer:Identity", "1, 1"),
            //        Category = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
            //        assortment_type = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
            //        Name = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
            //        Brand = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
            //        Model_ID = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
            //        SKU_ID = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
            //        Target_Age_Groups = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
            //        Target_Genders = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
            //        Season = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
            //        Description = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true),
            //        Material_Upper_Material_Clothing1_Ratio = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
            //        Material_Upper_Material_Clothing1 = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
            //        Material_Upper_Material_Clothing2_Ratio = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
            //        Material_Upper_Material_Clothing2 = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
            //        Material_Upper_Material_Clothing3_Ratio = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
            //        Material_Upper_Material_Clothing3 = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
            //        Material_Upper_Material_Clothing4_Ratio = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
            //        Material_Upper_Material_Clothing4 = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
            //        Media1 = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
            //        Media2 = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
            //        Media3 = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
            //        Media4 = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
            //        Media5 = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
            //        Color_Code_Primary = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
            //        Supplier_Color = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
            //        Size = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
            //        EAN = table.Column<string>(type: "nvarchar(13)", maxLength: 13, nullable: false),
            //        Stock_Quantity = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "0"),
            //        regular_price = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
            //        Discount = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
            //        Currency = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
            //        LanguageCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
            //        CargoDesi = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
            //        DeliveryCosts = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
            //        UpdateDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
            //        RecordDate = table.Column<DateTime>(type: "datetime2", nullable: true, defaultValueSql: "GETUTCDATE()")
            //    },
            //    constraints: table =>
            //    {
            //        table.PrimaryKey("PK_Products_XONEsource_Urunler", x => x.ID);
            //    });

            migrationBuilder.CreateTable(
                name: "iks_Orders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    OrderId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OrderNumber = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    OrderDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Status = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    TotalPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Currency = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    PaymentMethod = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CustomerId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_iks_Orders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_iks_Orders_iks_Customers_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "iks_Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "iks_OrderItems",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ProductId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProductName = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Quantity = table.Column<int>(type: "int", nullable: false),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    OrderId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_iks_OrderItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_iks_OrderItems_iks_Orders_OrderId",
                        column: x => x.OrderId,
                        principalTable: "iks_Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "iks_Refunds",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    OrderId = table.Column<int>(type: "int", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Reason = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    RefundDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_iks_Refunds", x => x.Id);
                    table.ForeignKey(
                        name: "FK_iks_Refunds_iks_Orders_OrderId",
                        column: x => x.OrderId,
                        principalTable: "iks_Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_iks_OrderItems_OrderId",
                table: "iks_OrderItems",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_iks_Orders_CustomerId",
                table: "iks_Orders",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_iks_Refunds_OrderId",
                table: "iks_Refunds",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_Products_EAN",
                table: "Products_XONEsource_Urunler",
                column: "EAN",
                unique: true,
                filter: "[EAN] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Products_SKU_ID",
                table: "Products_XONEsource_Urunler",
                column: "SKU_ID",
                unique: true,
                filter: "[SKU_ID] IS NOT NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "iks_OrderItems");

            migrationBuilder.DropTable(
                name: "iks_Products");

            migrationBuilder.DropTable(
                name: "iks_Refunds");

            migrationBuilder.DropTable(
                name: "Products_XONEsource_Urunler");

            migrationBuilder.DropTable(
                name: "iks_Orders");

            migrationBuilder.DropTable(
                name: "iks_Customers");
        }
    }
}
