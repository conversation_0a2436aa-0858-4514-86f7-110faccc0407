﻿using System.Text.Json.Serialization;

namespace XECOM_Client_CloudServer.Models
{
    // Ürün de<PERSON>ikliklerini temsil eder
    public class ProductChange
    {
        // Değişiklik yapılan ürün
        [JsonPropertyName("product")]
        public Product Product { get; set; }

        // Değişiklik türü (I: Insert, U: Update, D: Delete)
        [JsonPropertyName("changeOperation")]
        public string ChangeOperation { get; set; }
    }
}
